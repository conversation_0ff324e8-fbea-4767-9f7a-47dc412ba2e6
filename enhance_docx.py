#!/usr/bin/env python3
"""
Enhance the generated DOCX file with better formatting
"""

import sys
import os
from datetime import datetime

try:
    from docx import Document
    from docx.shared import Inches, Pt, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
    from docx.oxml.shared import OxmlElement, qn
except ImportError:
    print("python-docx not found. Installing...")
    os.system("pip3 install python-docx")
    try:
        from docx import Document
        from docx.shared import Inches, Pt, RGBColor
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.style import WD_STYLE_TYPE
        from docx.oxml.shared import OxmlElement, qn
    except ImportError:
        print("Failed to install python-docx. Please install manually:")
        print("pip3 install python-docx")
        sys.exit(1)

def add_page_number(section):
    """Add page numbers to footer"""
    footer = section.footer
    footer_para = footer.paragraphs[0]
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Clear existing content
    footer_para.clear()
    
    # Add page number
    run = footer_para.runs[0] if footer_para.runs else footer_para.add_run()
    run.text = "Page "
    
    # Add page number field
    fldChar1 = OxmlElement('w:fldChar')
    fldChar1.set(qn('w:fldCharType'), 'begin')
    
    instrText = OxmlElement('w:instrText')
    instrText.text = "PAGE"
    
    fldChar2 = OxmlElement('w:fldChar')
    fldChar2.set(qn('w:fldCharType'), 'end')
    
    run._element.append(fldChar1)
    run._element.append(instrText)
    run._element.append(fldChar2)

def enhance_document(docx_file):
    """Enhance the DOCX document with better formatting"""
    try:
        print(f"Enhancing {docx_file}...")
        
        # Open the document
        doc = Document(docx_file)
        
        # Set document margins
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)
            
            # Add page numbers
            add_page_number(section)
        
        # Add header with document title and date
        header = sections[0].header
        header_para = header.paragraphs[0]
        header_para.clear()
        
        # Document title in header
        title_run = header_para.add_run("S32DS DevOps & Automation Testing Project Requirements")
        title_run.font.size = Pt(10)
        title_run.font.color.rgb = RGBColor(0x40, 0x40, 0x40)
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add date line
        date_para = header.add_paragraph()
        date_run = date_para.add_run(f"Generated: {datetime.now().strftime('%B %d, %Y')}")
        date_run.font.size = Pt(8)
        date_run.font.color.rgb = RGBColor(0x80, 0x80, 0x80)
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Enhance styles
        styles = doc.styles
        
        # Enhance heading styles
        if 'Heading 1' in styles:
            heading1 = styles['Heading 1']
            heading1.font.color.rgb = RGBColor(0x1f, 0x4e, 0x79)
            heading1.font.size = Pt(16)
        
        if 'Heading 2' in styles:
            heading2 = styles['Heading 2']
            heading2.font.color.rgb = RGBColor(0x2e, 0x75, 0xb6)
            heading2.font.size = Pt(14)
        
        if 'Heading 3' in styles:
            heading3 = styles['Heading 3']
            heading3.font.color.rgb = RGBColor(0x5b, 0x9b, 0xd5)
            heading3.font.size = Pt(12)
        
        # Enhance normal text
        if 'Normal' in styles:
            normal = styles['Normal']
            normal.font.name = 'Calibri'
            normal.font.size = Pt(11)
            normal.paragraph_format.line_spacing = 1.15
            normal.paragraph_format.space_after = Pt(6)
        
        # Save the enhanced document
        doc.save(docx_file)
        print(f"✅ Enhanced formatting applied to {docx_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enhancing document: {e}")
        return False

def main():
    docx_file = "S32DS_DevOps_Project_Requirements.docx"
    
    if not os.path.exists(docx_file):
        print(f"❌ File {docx_file} not found!")
        print("Please run the conversion first:")
        print("pandoc project-requirements.md -o S32DS_DevOps_Project_Requirements.docx --toc --toc-depth=3")
        sys.exit(1)
    
    if enhance_document(docx_file):
        print(f"\n🎉 Document enhancement completed!")
        print(f"📄 Enhanced file: {docx_file}")
        print(f"📁 Location: {os.path.abspath(docx_file)}")
        print(f"📊 File size: {os.path.getsize(docx_file):,} bytes")
    else:
        print("❌ Enhancement failed")
        sys.exit(1)

if __name__ == "__main__":
    main()

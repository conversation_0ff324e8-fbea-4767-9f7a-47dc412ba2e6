#!/usr/bin/env python3
"""
Convert project-requirements.md to DOCX format
"""

import os
import sys
from pathlib import Path

try:
    import pypandoc
    import docx
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
except ImportError as e:
    print(f"Missing required packages. Please install them:")
    print("pip install pypandoc python-docx")
    print(f"Error: {e}")
    sys.exit(1)

def convert_md_to_docx(input_file, output_file):
    """
    Convert markdown file to DOCX using pypandoc
    """
    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            print(f"Error: Input file '{input_file}' not found")
            return False
        
        print(f"Converting {input_file} to {output_file}...")
        
        # Convert using pypandoc
        pypandoc.convert_file(
            input_file, 
            'docx', 
            outputfile=output_file,
            extra_args=[
                '--reference-doc=template.docx' if os.path.exists('template.docx') else '',
                '--toc',
                '--toc-depth=3',
                '--highlight-style=github'
            ]
        )
        
        print(f"Successfully converted to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return False

def enhance_docx(docx_file):
    """
    Enhance the generated DOCX with better formatting
    """
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        doc = Document(docx_file)
        
        # Set document margins
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
        
        # Add header
        header = sections[0].header
        header_para = header.paragraphs[0]
        header_para.text = "S32DS DevOps & Automation Testing Project Requirements"
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add footer with page numbers
        footer = sections[0].footer
        footer_para = footer.paragraphs[0]
        footer_para.text = "Page "
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Save the enhanced document
        doc.save(docx_file)
        print(f"Enhanced formatting applied to {docx_file}")
        
    except Exception as e:
        print(f"Warning: Could not enhance document formatting: {e}")

def main():
    input_file = "project-requirements.md"
    output_file = "S32DS_DevOps_Project_Requirements.docx"
    
    # Check if pandoc is installed
    try:
        pypandoc.get_pandoc_version()
    except OSError:
        print("Error: Pandoc is not installed or not found in PATH")
        print("Please install Pandoc from: https://pandoc.org/installing.html")
        print("Or use package manager:")
        print("  macOS: brew install pandoc")
        print("  Ubuntu: sudo apt-get install pandoc")
        print("  Windows: Download from pandoc.org")
        sys.exit(1)
    
    # Convert the file
    if convert_md_to_docx(input_file, output_file):
        # Enhance the document
        enhance_docx(output_file)
        print(f"\n✅ Conversion completed successfully!")
        print(f"📄 Output file: {output_file}")
        print(f"📁 Location: {os.path.abspath(output_file)}")
    else:
        print("❌ Conversion failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
